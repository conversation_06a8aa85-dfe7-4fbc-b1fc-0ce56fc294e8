#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
用于测试qwen-turbo模型的连接和响应
"""

import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_connection():
    """测试API连接"""
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试用的简单内容
    test_content = "今天天气很好，适合出门散步。"
    
    prompt = f"""
请对以下文本内容进行分析，判断是否包含敏感信息：

文本内容：{test_content}

请严格按照以下JSON格式回复：
{{
    "is_sensitive": false,
    "category": "无敏感内容",
    "reason": "这是正常的日常对话内容",
    "severity": 0
}}

其中：
- is_sensitive: true/false，是否包含敏感内容
- category: 敏感内容的分类
- reason: 判断理由
- severity: 敏感程度评分(0-10)
"""

    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "group": "ssvip",
        "temperature": 0.7,
        "max_tokens": 500,
        "top_p": 1,
        "frequency_penalty": 0,
        "presence_penalty": 0
    }
    
    print(f"正在测试API连接...")
    print(f"API地址: {API_URL}")
    print(f"使用模型: {MODEL}")
    print(f"测试内容: {test_content}")
    print("-" * 50)
    
    try:
        # 创建会话
        session = requests.Session()
        session.verify = False
        
        response = session.post(
            f"{API_URL}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API响应成功！")
            print(f"完整响应: {result}")

            # 检查响应结构
            if 'choices' in result and len(result['choices']) > 0:
                content_result = result['choices'][0]['message']['content'].strip()
                print(f"提取的内容: {content_result}")
            else:
                print("响应中没有找到choices字段或choices为空")
                print("这可能是因为模型返回了空响应")
                return False
            print("-" * 50)
            
            # 尝试解析JSON
            try:
                analysis = json.loads(content_result)
                print("JSON解析成功！")
                print(f"是否敏感: {analysis.get('is_sensitive', 'N/A')}")
                print(f"分类: {analysis.get('category', 'N/A')}")
                print(f"理由: {analysis.get('reason', 'N/A')}")
                print(f"敏感程度: {analysis.get('severity', 'N/A')}")
                print("\n✅ API测试成功！模型工作正常。")
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print("API返回的不是有效的JSON格式")
                return False
                
        elif response.status_code == 400:
            error_info = response.json().get('error', {})
            error_message = error_info.get('message', '')
            
            print(f"❌ API请求失败 (400): {error_message}")
            
            if 'Content security warning' in error_message or 'data_inspection_failed' in error_message:
                print("这是内容安全检查拦截，说明API连接正常，但内容被认为敏感")
                print("对于敏感内容分析项目，这种响应是正常的")
                return True
            else:
                return False
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.SSLError as e:
        print(f"❌ SSL连接错误: {e}")
        print("建议检查网络连接或尝试使用VPN")
        return False
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 网络连接错误: {e}")
        print("建议检查网络连接或API地址是否正确")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
        print("建议检查网络状况或增加超时时间")
        return False
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_sensitive_content():
    """测试敏感内容检测"""
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试一个可能敏感的内容
    test_content = "政府政策需要改进"
    
    prompt = f"""
请对以下文本内容进行分析，判断是否包含敏感信息：

文本内容：{test_content}

请严格按照以下JSON格式回复：
{{
    "is_sensitive": true,
    "category": "政治敏感",
    "reason": "涉及政府政策评价",
    "severity": 3
}}
"""

    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "group": "ssvip",
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    print(f"\n正在测试敏感内容检测...")
    print(f"测试内容: {test_content}")
    print("-" * 50)
    
    try:
        session = requests.Session()
        session.verify = False
        
        response = session.post(
            f"{API_URL}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 400:
            error_info = response.json().get('error', {})
            error_message = error_info.get('message', '')
            
            if 'Content security warning' in error_message or 'data_inspection_failed' in error_message:
                print("✅ 内容被API安全检查拦截，这说明：")
                print("1. API连接正常")
                print("2. qwen-turbo模型有内容安全检查机制")
                print("3. 对于被拦截的内容，我们的代码会自动标记为敏感内容")
                return True
        
        elif response.status_code == 200:
            result = response.json()
            content_result = result['choices'][0]['message']['content'].strip()
            print(f"API响应: {content_result}")
            return True
            
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("           API连接和模型测试")
    print("=" * 60)
    
    # 测试基本连接
    basic_test = test_api_connection()
    
    # 测试敏感内容
    sensitive_test = test_sensitive_content()
    
    print("\n" + "=" * 60)
    print("                测试总结")
    print("=" * 60)
    print(f"基本连接测试: {'✅ 通过' if basic_test else '❌ 失败'}")
    print(f"敏感内容测试: {'✅ 通过' if sensitive_test else '❌ 失败'}")
    
    if basic_test and sensitive_test:
        print("\n🎉 所有测试通过！你的API配置和qwen-turbo模型工作正常。")
        print("现在可以运行主分析程序了。")
    else:
        print("\n⚠️  部分测试失败，请检查网络连接和API配置。")
