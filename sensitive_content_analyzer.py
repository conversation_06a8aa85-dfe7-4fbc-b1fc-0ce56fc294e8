#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析脚本
调用大模型API分析Excel文件中的敏感信息
"""

import pandas as pd
import requests
import json
import time
import os
import sys
from typing import Dict, Tuple
import logging
import urllib3
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def print_and_flush(message):
    """打印消息并立即刷新输出缓冲区"""
    print(message)
    sys.stdout.flush()

# 配置日志 - 同时输出到控制台和文件
def setup_logging():
    """设置日志配置"""
    # 创建logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 清除已有的处理器
    logger.handlers.clear()

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器 - 显示所有INFO级别的日志
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器 - 普通日志
    file_handler = logging.FileHandler('analysis.log', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 文件处理器 - 错误日志
    error_handler = logging.FileHandler('error.log', encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)

    return logger

logger = setup_logging()

class SensitiveContentAnalyzer:
    def __init__(self, api_url: str, api_key: str, model: str, max_workers: int = 3):
        """
        初始化敏感内容分析器

        Args:
            api_url: API地址
            api_key: API密钥
            model: 模型名称
            max_workers: 最大并发线程数
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.max_workers = max_workers
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        # 线程锁，用于保护共享数据
        self.lock = threading.Lock()
        
    def analyze_content(self, content: str, max_retries: int = 3) -> Dict[str, str]:
        """
        分析内容是否包含敏感信息

        Args:
            content: 要分析的内容
            max_retries: 最大重试次数

        Returns:
            包含分析结果的字典
        """
        prompt = f"""
请对以下文本内容进行深度语义分析，理解其真实含义和上下文，然后判断是否包含敏感信息并进行精确分类。

重要提示：请进行综合分析，不要仅根据关键词匹配，要理解内容的整体语义和真实意图。

文本内容："{content}"

请按照以下格式回复（只回复JSON格式，不要其他内容）：
{{
    "is_sensitive": true/false,
    "category": "具体标签名称",
    "severity": 1-10,
    "reason": "详细说明判断理由和语义分析过程"
}}

敏感内容标签分类（请从以下标签中选择一个具体的标签名称）：

8964、反共、反华、民运、其它涉政、涉藏、涉港、涉疆、涉台、涉政、涉政组织、维权、辱共、辱华、辱习、上访、安全、暴恐、爆炸物、毒品、枪支、危险化学品、无差别事件、涉刀、涉军、黑恶、假币、强奸、窃听、窃照、偷税漏税、洗钱、邪教、敲诈勒索、偷盗、偷渡、走私、暗网、翻墙、网络安全、虚拟币、接码、两卡、敏感应用、涉黄、恋童、字母圈、毒疫苗、郭文贵、精神病药物、骗子、新冠肺炎、抑郁症药物、隐私数据、赌博、妇女儿童、换汇、假货、假药、普通股票、器官、烟草、移民、引流、宗教、作弊

分析要点：
1. 优先分析内容的主要意图和核心语义，不要过度敏感
2. 精准区分"辱华"和"辱习"：
   - 针对习近平个人的辱骂、攻击、恶意谣言 → "辱习"
   - 针对中华民族、中国人民、中国文化的辱骂 → "辱华"
   - 针对其他中国国家领导人（如李克强、王岐山等）的直接辱骂、人身攻击 → "辱华"
   - 针对国家制度、政府的恶意攻击 → "辱华"
3. 对于涉及习近平的内容，需要精准判断：
   - 直接辱骂习近平、人身攻击、恶意编造关于习近平的谣言 → "辱习"
   - 客观新闻报道、正常外交活动报道、官方政策宣传 → "无敏感内容"
   - 政治批评、政策质疑、负面评价 → "涉政"
4. 正常的新闻报道（如外交访问、政策宣布、官方活动）不应被视为敏感内容
5. 只有当内容包含明显的批评、质疑、负面评价时才归类为"涉政"
6. 如果包含多种敏感元素，选择最核心、最主要的分类
7. 不要被表面的关键词误导，要理解深层含义和真实意图
8. category字段必须是上述标签列表中的一个具体标签名称，不能是组合或描述
9. 如果不涉及敏感内容，category必须填写"无敏感内容"

severity评分标准：
- 直接辱华、辱习、编造恶意谣言：9-10分
- 严重政治敏感内容、煽动颠覆：7-8分
- 一般政治敏感内容、政策批评：5-6分
- 客观新闻报道涉及敏感话题：3-4分
- 其他轻微敏感内容：1-2分
- 无敏感内容：0分

请严格按照JSON格式回复，进行深度语义分析后给出准确分类。
"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "group": "ssvip",
            "temperature": 0.7,
            "max_tokens": 500,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }
        
        for attempt in range(max_retries):
            try:
                # 创建会话以复用连接
                session = requests.Session()
                session.verify = False

                response = session.post(
                    f"{self.api_url}/v1/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=60,  # 增加超时时间
                )

                if response.status_code == 200:
                    result = response.json()
                    content_result = result['choices'][0]['message']['content'].strip()

                    # 尝试解析JSON响应
                    try:
                        analysis = json.loads(content_result)
                        return analysis
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析API响应为JSON (尝试{attempt+1}/{max_retries}): {content_result}")
                        if attempt == max_retries - 1:
                            return {
                                "is_sensitive": False,
                                "category": "无敏感内容",
                                "reason": "API响应格式错误",
                                "error": True
                            }
                elif response.status_code == 400:
                    # 处理内容安全警告
                    error_info = response.json().get('error', {})
                    error_message = error_info.get('message', '')

                    if 'Content security warning' in error_message or 'data_inspection_failed' in error_message:
                        logger.warning(f"内容被API安全检查拦截: {content[:100]}...")
                        return {
                            "is_sensitive": True,
                            "category": "涉政言论",
                            "reason": "内容被API安全检查识别为敏感内容",
                            "severity": 8,
                            "error": False
                        }
                    else:
                        logger.error(f"API请求失败 (尝试{attempt+1}/{max_retries}): {response.status_code} - {response.text}")
                        if attempt == max_retries - 1:
                            return {
                                "is_sensitive": False,
                                "category": "无敏感内容",
                                "reason": "API请求失败",
                                "error": True
                            }
                else:
                    logger.error(f"API请求失败 (尝试{attempt+1}/{max_retries}): {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:
                        return {
                            "is_sensitive": False,
                            "category": "无敏感内容",
                            "reason": "API请求失败",
                            "error": True
                        }

            except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
                logger.error(f"网络连接错误 (尝试{attempt+1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    return {
                        "is_sensitive": False,
                        "category": "无敏感内容",
                        "reason": f"网络连接失败: {str(e)}",
                        "error": True
                    }
            except requests.exceptions.Timeout as e:
                logger.error(f"请求超时 (尝试{attempt+1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    return {
                        "is_sensitive": False,
                        "category": "无敏感内容",
                        "reason": f"请求超时: {str(e)}",
                        "error": True
                    }
            except Exception as e:
                logger.error(f"分析内容时发生未知错误 (尝试{attempt+1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    return {
                        "is_sensitive": False,
                        "category": "无敏感内容",
                        "reason": f"分析失败: {str(e)}",
                        "error": True
                    }

            # 重试前等待
            if attempt < max_retries - 1:
                wait_time = 3 + attempt * 2  # 增加等待时间，网络问题需要更长等待
                logger.info(f"等待{wait_time}秒后重试...")
                time.sleep(wait_time)

    def analyze_single_row(self, row_data: Dict, index: int, total_count: int) -> Dict:
        """
        分析单行数据

        Args:
            row_data: 行数据字典
            index: 行索引
            total_count: 总行数

        Returns:
            分析结果字典
        """
        content = str(row_data['D']) if pd.notna(row_data['D']) else ""

        if not content or content.strip() == "" or content == "nan":
            return {
                'status': 'skipped',
                'index': index,
                'reason': '内容为空'
            }

        # 线程安全的进度输出
        with self.lock:
            progress = ((index + 1) / total_count) * 100
            progress_msg = f"进度: {index + 1}/{total_count} ({progress:.1f}%) - 分析第{index+1}行"
            print_and_flush(progress_msg)
            logger.info(progress_msg)

            content_msg = f"正在分析第{index+1}行内容: {content[:100]}..."
            print_and_flush(content_msg)
            logger.info(content_msg)

        # 调用API分析内容
        analysis = self.analyze_content(content)

        # 检查是否分析失败
        if analysis.get('error', False):
            failed_record = {
                'id': row_data['A'],
                '发言内容': row_data['D'],
                '时间': row_data['C'],
                '群组': row_data['B'],
                '失败原因': analysis.get('reason', '未知错误'),
                '行号': index + 1
            }

            with self.lock:
                error_msg = f"第{index+1}行分析失败: {analysis.get('reason')}"
                print_and_flush(f"错误: {error_msg}")
                logger.error(error_msg)

                fail_content_msg = f"失败内容: {content[:50]}..."
                print_and_flush(fail_content_msg)
                logger.info(fail_content_msg)

            return {
                'status': 'failed',
                'index': index,
                'failed_record': failed_record
            }

        # 如果检测到敏感内容
        if analysis.get('is_sensitive', False):
            user_id = row_data['A']
            category = analysis.get('category', '其他敏感')
            severity = analysis.get('severity', 1)

            sensitive_data = {
                'id': user_id,
                '敏感言论': row_data['D'],
                '时间': row_data['C'],
                '群组': row_data['B'],
                '敏感类型': category,
                '敏感程度': severity
            }

            with self.lock:
                if category == '涉政言论':
                    detect_msg = f"检测到涉政言论 (敏感度{severity}): {content[:50]}..."
                    print_and_flush(detect_msg)
                    logger.info(detect_msg)
                else:
                    detect_msg = f"检测到{category} (敏感度{severity}): {content[:50]}..."
                    print_and_flush(detect_msg)
                    logger.info(detect_msg)

            return {
                'status': 'sensitive',
                'index': index,
                'user_id': user_id,
                'category': category,
                'severity': severity,
                'sensitive_data': sensitive_data
            }
        else:
            # 未检测到敏感内容
            with self.lock:
                no_sensitive_msg = f"第{index+1}行未检测到敏感内容"
                print_and_flush(no_sensitive_msg)
                logger.info(no_sensitive_msg)

            return {
                'status': 'clean',
                'index': index
            }
    
    def process_excel_file(self, file_path: str, max_rows: int = None) -> Tuple[pd.DataFrame, pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        处理Excel文件，分析敏感内容

        Args:
            file_path: Excel文件路径
            max_rows: 最大分析行数，None表示分析全部

        Returns:
            涉政言论DataFrame、其他敏感内容总DataFrame、分类敏感内容字典和失败记录DataFrame的元组
        """
        try:
            # 读取Excel文件，不使用第一行作为列名
            df = pd.read_excel(file_path, sheet_name='Sheet1', header=None)
            read_msg = f"成功读取Excel文件，共{len(df)}行数据"
            print_and_flush(read_msg)
            logger.info(read_msg)

            # 重命名列为A、B、C、D等
            column_names = ['A', 'B', 'C', 'D', 'E'][:len(df.columns)]
            df.columns = column_names

            column_msg = f"Excel文件列结构: {list(df.columns)}"
            print_and_flush(column_msg)
            logger.info(column_msg)

            preview_msg = "前3行数据预览:"
            print_and_flush(preview_msg)
            logger.info(preview_msg)

            preview_data = df.head(3).to_string()
            print_and_flush(preview_data)
            logger.info(preview_data)

            # 初始化结果字典
            political_sensitive = []  # 涉政言论
            other_sensitive_all = []  # 其他敏感内容总表
            categorized_sensitive = {  # 分类敏感内容
                '色情内容': [],
                '暴力内容': [],
                '歧视言论': [],
                '谣言传播': [],
                '诈骗信息': [],
                '仇恨言论': [],
                '违法内容': [],
                '其他敏感': []
            }

            # 用于去重的字典：存储每个ID的最敏感内容
            id_max_sensitive = {}  # {id: {'data': row_data, 'severity': severity, 'category': category}}

            # 失败记录列表
            failed_records = []  # 存储分析失败的记录

            # 限制分析行数
            total_rows = len(df)
            if max_rows:
                df = df.head(max_rows)
                limit_msg = f"限制分析行数: {max_rows}/{total_rows}"
                print_and_flush(limit_msg)
                logger.info(limit_msg)

            # 遍历每一行进行分析
            total_to_analyze = len(df)
            analyzed_count = 0

            start_msg = f"开始分析 {total_to_analyze} 行数据..."
            print_and_flush(start_msg)
            logger.info(start_msg)

            for index, row in df.iterrows():
                # D列是发言内容（第4列，索引为3）
                content = str(row['D']) if pd.notna(row['D']) else ""

                if not content or content.strip() == "" or content == "nan":
                    continue

                analyzed_count += 1
                progress = (analyzed_count / total_to_analyze) * 100
                # 同时输出到控制台和日志
                progress_msg = f"进度: {analyzed_count}/{total_to_analyze} ({progress:.1f}%) - 分析第{index+1}行"
                print_and_flush(progress_msg)
                logger.info(progress_msg)

                content_msg = f"正在分析第{index+1}行内容: {content[:100]}..."
                print_and_flush(content_msg)
                logger.info(content_msg)

                # 调用API分析内容
                analysis = self.analyze_content(content)

                # 检查是否分析失败
                if analysis.get('error', False):
                    failed_record = {
                        'id': row['A'],
                        '发言内容': row['D'],
                        '时间': row['C'],
                        '群组': row['B'],
                        '失败原因': analysis.get('reason', '未知错误'),
                        '行号': index + 1
                    }
                    failed_records.append(failed_record)
                    error_msg = f"第{index+1}行分析失败: {analysis.get('reason')}"
                    print_and_flush(f"错误: {error_msg}")
                    logger.error(error_msg)

                    fail_content_msg = f"失败内容: {content[:50]}..."
                    print_and_flush(fail_content_msg)
                    logger.info(fail_content_msg)
                    continue

                # 如果检测到敏感内容
                if analysis.get('is_sensitive', False):
                    user_id = row['A']
                    category = analysis.get('category', '其他敏感')
                    severity = analysis.get('severity', 1)

                    sensitive_data = {
                        'id': user_id,
                        '敏感言论': row['D'],
                        '时间': row['C'],
                        '群组': row['B'],
                        '敏感类型': category,
                        '敏感程度': severity
                    }

                    # 处理涉政言论
                    if category == '涉政言论':
                        # 检查是否已有该ID的涉政言论
                        if user_id not in id_max_sensitive or id_max_sensitive[user_id]['category'] != '涉政言论':
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                        elif severity > id_max_sensitive[user_id]['severity']:
                            # 更新为更敏感的内容
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                            logger.info(f"ID {user_id} 更新为更敏感的涉政言论 (敏感度{severity}): {content[:50]}...")
                        else:
                            logger.info(f"ID {user_id} 已有更敏感的涉政言论，跳过当前记录 (敏感度{severity})")

                        if user_id not in id_max_sensitive or id_max_sensitive[user_id]['category'] != '涉政言论':
                            detect_msg = f"检测到涉政言论 (敏感度{severity}): {content[:50]}..."
                            print_and_flush(detect_msg)
                            logger.info(detect_msg)

                    # 处理其他敏感内容
                    else:
                        # 检查是否已有该ID的记录
                        if user_id not in id_max_sensitive:
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                            detect_msg = f"检测到{category} (敏感度{severity}): {content[:50]}..."
                            print_and_flush(detect_msg)
                            logger.info(detect_msg)
                        elif id_max_sensitive[user_id]['category'] != '涉政言论' and severity > id_max_sensitive[user_id]['severity']:
                            # 如果不是涉政言论且当前更敏感，则更新
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                            update_msg = f"ID {user_id} 更新为更敏感的{category} (敏感度{severity}): {content[:50]}..."
                            print_and_flush(update_msg)
                            logger.info(update_msg)
                        else:
                            skip_msg = f"ID {user_id} 已有更敏感的记录，跳过当前{category} (敏感度{severity})"
                            print_and_flush(skip_msg)
                            logger.info(skip_msg)

                else:
                    # 未检测到敏感内容
                    no_sensitive_msg = f"第{index+1}行未检测到敏感内容"
                    print_and_flush(no_sensitive_msg)
                    logger.info(no_sensitive_msg)

                # 添加延迟避免API限制
                time.sleep(0.5)

            # 从去重字典中提取最终结果
            for user_id, record in id_max_sensitive.items():
                data = record['data']
                category = record['category']

                if category == '涉政言论':
                    political_sensitive.append(data)
                else:
                    other_sensitive_all.append(data)
                    # 同时添加到对应的分类中
                    if category in categorized_sensitive:
                        categorized_sensitive[category].append(data)
                    else:
                        categorized_sensitive['其他敏感'].append(data)

            # 创建DataFrame
            political_df = pd.DataFrame(political_sensitive)
            other_all_df = pd.DataFrame(other_sensitive_all)
            failed_df = pd.DataFrame(failed_records)

            # 创建分类DataFrame字典
            categorized_dfs = {}
            for cat_name, cat_data in categorized_sensitive.items():
                if cat_data:  # 只创建有数据的分类
                    categorized_dfs[cat_name] = pd.DataFrame(cat_data)

            logger.info(f"分析完成！涉政言论: {len(political_df)}条，其他敏感内容: {len(other_all_df)}条，失败记录: {len(failed_df)}条")
            logger.info(f"其他敏感内容分类: {', '.join([f'{k}({len(v)})' for k, v in categorized_dfs.items()])}")

            if len(failed_df) > 0:
                logger.warning(f"警告: 有{len(failed_df)}条记录分析失败，详情请查看失败记录文件")

            return political_df, other_all_df, categorized_dfs, failed_df

        except Exception as e:
            logger.error(f"处理Excel文件时发生错误: {str(e)}")
            raise
    
    def save_results(self, political_df: pd.DataFrame, other_all_df: pd.DataFrame, categorized_dfs: Dict[str, pd.DataFrame], failed_df: pd.DataFrame, output_file: str):
        """
        保存分析结果到Excel文件

        Args:
            political_df: 涉政言论DataFrame
            other_all_df: 其他敏感内容总DataFrame
            categorized_dfs: 分类敏感内容DataFrame字典
            failed_df: 失败记录DataFrame
            output_file: 输出文件路径
        """
        # 尝试多次保存，处理文件占用问题
        max_retries = 3
        saved = False

        for attempt in range(max_retries):
            try:
                # 如果文件被占用，尝试使用带时间戳的文件名
                current_output_file = output_file
                if attempt > 0:
                    timestamp = int(time.time())
                    current_output_file = output_file.replace('.xlsx', f'_备份{timestamp}.xlsx')
                    logger.info(f"尝试保存到备用文件: {current_output_file}")

                with pd.ExcelWriter(current_output_file, engine='openpyxl') as writer:
                    # 保存涉政言论
                    political_df.to_excel(writer, sheet_name='涉政言论', index=False)

                    # 保存其他敏感内容总表
                    other_all_df.to_excel(writer, sheet_name='其他敏感内容', index=False)

                    # 保存各分类的敏感内容
                    for category, df in categorized_dfs.items():
                        # Excel工作表名称有长度限制，截断过长的名称
                        sheet_name = category[:31] if len(category) > 31 else category
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 保存失败记录
                    if len(failed_df) > 0:
                        failed_df.to_excel(writer, sheet_name='分析失败记录', index=False)

                        # 创建失败记录的原始数据表，方便重新分析
                        failed_original_data = []
                        for _, row in failed_df.iterrows():
                            failed_original_data.append({
                                'A': row['id'],
                                'B': row['群组'],
                                'C': row['时间'],
                                'D': row['发言内容'],
                                'E': ''  # 空列
                            })

                        if failed_original_data:
                            failed_original_df = pd.DataFrame(failed_original_data)
                            failed_original_df.to_excel(writer, sheet_name='失败记录原始数据', index=False)
                            logger.info(f"失败记录原始数据已保存，可用于重新分析")

                # 同时保存失败记录到单独的CSV文件
                if len(failed_df) > 0:
                    failed_csv_file = current_output_file.replace('.xlsx', '_失败记录.csv')
                    failed_df.to_csv(failed_csv_file, index=False, encoding='utf-8-sig')
                    logger.info(f"失败记录已单独保存到: {failed_csv_file}")

                logger.info(f"结果已保存到: {current_output_file}")
                sheet_list = ['涉政言论', '其他敏感内容'] + list(categorized_dfs.keys())
                if len(failed_df) > 0:
                    sheet_list.extend(['分析失败记录', '失败记录原始数据'])
                logger.info(f"包含工作表: {', '.join(sheet_list)}")

                saved = True
                break

            except PermissionError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"文件被占用，等待3秒后重试 (尝试{attempt+1}/{max_retries})")
                    logger.warning("请关闭Excel等程序中打开的结果文件")
                    time.sleep(3)
                else:
                    logger.error(f"文件保存失败，请关闭Excel等程序后重试: {str(e)}")
                    # 最后尝试保存到CSV格式
                    try:
                        csv_file = output_file.replace('.xlsx', '_敏感内容.csv')
                        other_all_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                        logger.info(f"已保存敏感内容到CSV文件: {csv_file}")

                        if len(failed_df) > 0:
                            failed_csv = output_file.replace('.xlsx', '_失败记录.csv')
                            failed_df.to_csv(failed_csv, index=False, encoding='utf-8-sig')
                            logger.info(f"已保存失败记录到CSV文件: {failed_csv}")

                        saved = True
                        break
                    except Exception as csv_e:
                        logger.error(f"CSV保存也失败: {str(csv_e)}")

            except Exception as e:
                logger.error(f"保存结果时发生其他错误: {str(e)}")
                break

        if not saved:
            raise Exception("无法保存结果文件，请检查文件权限或关闭占用文件的程序")

def get_input_file():
    """获取用户输入的文件名"""
    print("=== 敏感内容分析工具 ===")
    print("\n请指定要分析的Excel文件:")
    print("示例文件名: 聊天记录.xlsx, 群组消息.xlsx, data.xlsx")
    print("注意: 文件应包含Sheet1工作表，且包含A、B、C、D列数据")

    while True:
        filename = input("\n请输入Excel文件名 (默认: 5.xlsx): ").strip()

        # 如果用户直接回车，使用默认文件名
        if not filename:
            filename = "5.xlsx"

        # 自动添加.xlsx扩展名（如果用户没有输入）
        if not filename.lower().endswith('.xlsx'):
            filename += '.xlsx'

        # 检查文件是否存在
        if os.path.exists(filename):
            print(f"成功: 找到文件: {filename}")
            return filename
        else:
            print(f"错误: 文件不存在: {filename}")
            print("请检查文件名是否正确，或将文件放在当前目录中")
            try:
                retry = input("是否重新输入? (y/n): ").strip().lower()
                if retry != 'y':
                    return None
            except (EOFError, KeyboardInterrupt):
                return None

def validate_file(filename):
    """验证文件是否存在并返回标准化的文件名"""
    if not filename:
        return None

    # 自动添加.xlsx扩展名（如果用户没有输入）
    if not filename.lower().endswith('.xlsx'):
        filename += '.xlsx'

    # 检查文件是否存在
    if os.path.exists(filename):
        return filename
    else:
        print(f"错误: 文件不存在: {filename}")
        return None

def main():
    """主函数"""
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"

    # 获取输入文件 - 支持从标准输入或交互式输入
    INPUT_FILE = None

    # 尝试从标准输入读取文件名（用于脚本调用）
    try:
        import sys
        if not sys.stdin.isatty():  # 如果是管道输入
            filename_input = input().strip()
            if filename_input:
                INPUT_FILE = validate_file(filename_input)
                if INPUT_FILE:
                    print(f"成功: 使用文件: {INPUT_FILE}")
    except (EOFError, KeyboardInterrupt):
        pass

    # 如果没有从管道获取到有效文件名，则交互式获取
    if not INPUT_FILE:
        try:
            INPUT_FILE = get_input_file()
            if not INPUT_FILE:
                print("程序退出")
                return
        except (EOFError, KeyboardInterrupt):
            print("\n程序被用户中断")
            return

    # 生成输出文件名
    base_name = INPUT_FILE.replace('.xlsx', '')
    OUTPUT_FILE = f"{base_name}_敏感内容分析结果.xlsx"

    print(f"\n分析配置:")
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"使用模型: {MODEL}")

    # 询问用户是否要限制分析行数（用于测试）
    try:
        limit_input = input("\n是否限制分析行数？(输入数字限制行数，直接回车分析全部): ").strip()
        max_rows = int(limit_input) if limit_input else None
        if max_rows:
            print(f"将只分析前 {max_rows} 行数据")
    except ValueError:
        max_rows = None
        print("输入无效，将分析全部数据")
    except EOFError:
        max_rows = None
        print("使用默认设置，分析全部数据")

    try:
        # 创建分析器实例
        analyzer = SensitiveContentAnalyzer(API_URL, API_KEY, MODEL)

        # 处理Excel文件
        logger.info("开始分析Excel文件...")
        political_df, other_all_df, categorized_dfs, failed_df = analyzer.process_excel_file(INPUT_FILE, max_rows)

        # 保存结果
        analyzer.save_results(political_df, other_all_df, categorized_dfs, failed_df, OUTPUT_FILE)

        # 打印统计信息
        logger.info("="*50)
        logger.info("           分析任务完成")
        logger.info("="*50)
        logger.info(f"涉政言论数量: {len(political_df)}")
        logger.info(f"其他敏感内容总数量: {len(other_all_df)}")
        logger.info(f"分析失败记录数量: {len(failed_df)}")

        # 打印分类统计
        if categorized_dfs:
            logger.info("其他敏感内容分类统计:")
            for category, df in categorized_dfs.items():
                logger.info(f"  {category}: {len(df)}条")

        logger.info(f"结果已保存到: {OUTPUT_FILE}")

        if len(failed_df) > 0:
            failed_file = OUTPUT_FILE.replace('.xlsx', '_失败记录.csv')
            logger.warning(f"有{len(failed_df)}条记录分析失败")
            logger.info(f"失败记录已保存到: {failed_file}")
            logger.info("建议检查网络连接或稍后重试失败的记录")

        if len(political_df) > 0:
            logger.info("涉政言论示例:")
            logger.info(f"\n{political_df.head().to_string()}")

        if len(other_all_df) > 0:
            logger.info("其他敏感内容示例:")
            logger.info(f"\n{other_all_df.head().to_string()}")

        logger.info("="*50)
        logger.info("         分析完成，请查看结果文件")
        logger.info("="*50)

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
