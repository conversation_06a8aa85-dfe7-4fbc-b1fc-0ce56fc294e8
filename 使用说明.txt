敏感内容分析工具 - 快速使用指南
=====================================

📋 准备工作
-----------
1. 确保已安装Python 3.x
2. 将要分析的Excel文件放在当前目录
3. Excel文件应包含以下列：
   - A列：ID信息
   - B列：群组信息
   - C列：时间信息
   - D列：发言内容（待分析）

📁 支持的文件名
--------------
- 任意Excel文件名，如：聊天记录.xlsx, 群组消息.xlsx, data.xlsx
- 程序会自动提示输入文件名
- 默认文件名：5.xlsx（直接回车使用）

🚀 运行方式
-----------
方式：命令行运行
- 打开命令行，进入项目目录
- 运行：python run_analysis.py
- 按照提示操作

📊 分析模式
-----------
1. 测试模式：分析前50行（约2-3分钟）
2. 小规模分析：分析前500行（约20-30分钟）
3. 完整分析：分析全部数据（可能需要数小时）

📁 输出文件
-----------
- [文件名]_敏感内容分析结果.xlsx：主要结果文件
- analysis.log：完整分析日志
- error.log：错误日志
- [文件名]_敏感内容分析结果_失败记录.csv：失败记录（如有）

注：[文件名]为输入文件的名称（不含扩展名）

📋 结果说明
-----------
Excel结果文件包含以下工作表：
- 涉政言论：政治敏感内容
- 其他敏感内容：其他类型敏感内容汇总
- 色情内容、暴力内容等：各类型详细分类
- 分析失败记录：分析失败的记录（如有）

🔧 故障处理
-----------
如果有记录分析失败：
- 运行：python retry_failed.py
- 该脚本会重新尝试分析失败的记录

常见问题：
- API连接失败：检查网络连接，使用测试模式验证
- 文件读取失败：确保Excel文件格式正确
- 分析中断：查看日志文件，使用重试脚本处理失败记录

💡 注意事项
-----------
- 程序会自动去重，同ID只保留最敏感的一条记录
- 每条记录都有1-10的敏感程度评分
- 涉政言论优先级最高
- 建议先用测试模式验证功能正常
- 完整分析需要较长时间，请耐心等待

📞 技术支持
-----------
如遇问题，请查看：
1. error.log 错误日志文件
2. analysis.log 完整分析日志
3. README.md 详细说明文档
